"""
Django settings for backend project.

Generated by 'django-admin startproject' using Django 3.0.8.

For more information on this file, see
https://docs.djangoproject.com/en/3.0/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/3.0/ref/settings/
"""

import environ
import os
import datetime
from pathlib import Path
# import mimetypes
# from redis import ConnectionPool
# from huey import RedisHuey
import boto3

env = environ.Env(
    # set casting, default value
    DEBUG=(bool, False),
    STAGE=(str, 'development'),
    STATIC_HOST=(str, ''),
    RDS_DB_NAME=(str, 'expo'),
    RDS_USERNAME=(str, 'postgres'),
    RDS_PASSWORD=(str, 'postgres'),
    RDS_HOSTNAME=(str, 'db'),
    RDS_PORT=(int, 5432),
)

# Set the project base directory
BASE_DIR = Path(__file__).resolve(strict=True).parent.parent

# Take environment variables from .env file
environ.Env.read_env(BASE_DIR / '.env')

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/3.0/howto/deployment/checklist/

# SECURITY WARNING: don't run with debug turned on in production!
# False if not in os.environ because of casting above
DEBUG = env('DEBUG')

# SECURITY WARNING: keep the secret key used in production secret!
# If SECRET_KEY not in os.environ it raises Django's ImproperlyConfigured
SECRET_KEY = env('SECRET_KEY')


ALLOWED_HOSTS = ['*']


# Application definition
SHARED_APPS = (
    'django_tenants',
    'django.contrib.contenttypes',
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'rest_framework',
    'events',
    'channels',
    'corsheaders',
    'rest_framework_swagger',
    'users',
    'roles',
    'chat',
    'public_resources',
    'accounts',
    'rest_framework_api_key',
    'tinymce',
    'jwt_shared_secret',
    'django.contrib.postgres',
)

TENANT_APPS = (
    # The following Django contrib apps must be in TENANT_APPS
    'django.contrib.contenttypes',

    # tenant-specific apps
    'spaces',
    'storages',
    'expohall',
    'custom_fields',
    'subscriptions',
    'auditorium',
    'timeTriggers',
    'confirmation_page',
    'custom_page',
    'logs',
    'tags',
    'zoom',
    'credits',
    'polls'
)

INSTALLED_APPS = (
    'django_tenants',  # mandatory, should always be before any django app
    'django.contrib.contenttypes',
    # 'postgres_metrics.apps.PostgresMetrics',
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.postgres',
    'rest_framework',
    'events',
    'channels',
    'corsheaders',
    'users',
    'roles',
    'chat',
    'spaces',
    'storages',
    'expohall',
    'subscriptions',
    'auditorium',
    'custom_fields',
    'timeTriggers',
    'accounts',
    'rest_framework_api_key',
    'confirmation_page',
    'custom_page',
    'logs',
    'tags',
    'zoom',
    'public_resources',
    'tinymce',
    'jwt_shared_secret',
    'rest_framework_simplejwt',
    'credits',
    'polls',
    'extra_views',
    # 'huey.contrib.djhuey',
)
#INSTALLED_APPS = list(SHARED_APPS) + [app for app in TENANT_APPS if app not in SHARED_APPS]

MIDDLEWARE = [
    'django_tenants.middleware.main.TenantMainMiddleware', # Must be first
    'django.middleware.security.SecurityMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'backend.urls'

os.environ["DJANGO_ALLOW_ASYNC_UNSAFE"] = "true"

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [str(Path(BASE_DIR) / 'templates')],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.contrib.auth.context_processors.auth',
                'django.template.context_processors.debug',
                'django.template.context_processors.i18n',
                'django.template.context_processors.media',
                'django.template.context_processors.static',
                'django.template.context_processors.tz',
                'django.template.context_processors.request',
                'django.contrib.messages.context_processors.messages',
                'django.template.context_processors.csrf',
            ],
        },
    },
]

WSGI_APPLICATION = 'backend.wsgi.application'
ASGI_APPLICATION = 'backend.asgi.application'

EMAIL_HOST = env('EMAIL_HOST')
EMAIL_HOST_USER = env('EMAIL_HOST_USER')
EMAIL_HOST_PASSWORD = env('EMAIL_HOST_PASSWORD')
EMAIL_PORT = env('EMAIL_PORT')
EMAIL_USE_TLS = True

VIMEO_ACCESS_TOKEN = env('VIMEO_ACCESS_TOKEN')
VIMEO_CLIENT_ID = env('VIMEO_CLIENT_ID')
VIMEO_CLIENT_SECRET = env('VIMEO_CLIENT_SECRET')


QIQOCHAT_API_KEY = env('QIQOCHAT_API_KEY')
QIQOCHAT_API_SECRET = env('QIQOCHAT_API_SECRET')

# Database
# https://docs.djangoproject.com/en/3.0/ref/settings/#databases


DATABASES = {
    'default': {
        'ENGINE': 'django_tenants.postgresql_backend',
        'NAME': env('RDS_DB_NAME'),
        'USER': env('RDS_USERNAME'),
        'PASSWORD': env('RDS_PASSWORD'),
        'HOST': env('RDS_HOSTNAME'),
        'PORT': env('RDS_PORT'),
    }
}


DATABASE_ROUTERS = (
    'django_tenants.routers.TenantSyncRouter',
)

AUTH_USER_MODEL = 'users.CustomUser'
TENANT_MODEL = 'events.Event'
TENANT_DOMAIN_MODEL = 'events.Domain'
TENANT_LIMIT_SET_CALLS = True
TENANT_COLOR_ADMIN_APPS = True

# Password validation
# https://docs.djangoproject.com/en/3.0/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

AUTHENTICATION_BACKENDS = (
    'django.contrib.auth.backends.ModelBackend',
)


# Internationalization
# https://docs.djangoproject.com/en/3.0/topics/i18n/

LANGUAGE_CODE = 'en-us'
TIME_ZONE = 'America/Montreal'
USE_I18N = True
USE_L10N = True
USE_TZ = True


STREAM_API_KEY = env('STREAM_API_KEY')
STREAM_API_SECRET = env('STREAM_API_SECRET')

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/3.0/howto/static-files/

AWS_STORAGE_BUCKET_NAME = 'mbvs-server-v2-files'
AWS_S3_REGION_NAME = 'us-west-2'
AWS_ACCESS_KEY_ID = env('AWS_ACCESS_KEY_ID')
AWS_SECRET_ACCESS_KEY = env('AWS_SECRET_ACCESS_KEY')
AWS_S3_CUSTOM_DOMAIN = env('AWS_S3_CUSTOM_DOMAIN')
AWS_API_URL = env('AWS_API_URL')
AWS_API_KEY = env('AWS_API_KEY')

STAGE = env('STAGE')

# AWS CloudWatch configuration
AWS_CLOUDWATCH_LOG_GROUP = f"django-logs-{STAGE}" 
AWS_REGION = 'us-east-1'

boto3_logs_client = boto3.client('logs', region_name=AWS_REGION)

# Configure the logger
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module}: {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname}: {message}',
            'style': '{',
        },
    },
    'root': {
        'level': 'DEBUG',
        # Adding the watchtower handler here causes all loggers in the project that
        # have propagate=True (the default) to send messages to watchtower. If you
        # wish to send only from specific loggers instead, remove "watchtower" here
        # and configure individual loggers below.
        'handlers': ['console', 'watchtower'],
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
        },
        'watchtower': {
            'class': 'watchtower.CloudWatchLogHandler',
            'boto3_client': boto3_logs_client,
            'log_group_name': AWS_CLOUDWATCH_LOG_GROUP,
            'formatter': 'verbose',
            # Decrease the verbosity level here to send only those logs to watchtower,
            # but still see more verbose logs in the console. See the watchtower
            # documentation for other parameters that can be set here.
            'level': 'DEBUG',
            'create_log_group': True,
            'create_log_stream': True,
        }
    },
    'loggers': {
        # In the debug server (`manage.py runserver`), several Django system loggers cause
        # deadlocks when using threading in the logging handler, and are not supported by
        # watchtower. This limitation does not apply when running on production WSGI servers
        # (gunicorn, uwsgi, etc.), so we recommend that you set `propagate=True` below in your
        # production-specific Django settings file to receive Django system logs in CloudWatch.
        'django': {
            'level': 'INFO',
            'propagate': True,
        },
        'django.request': {
            'level': 'INFO',
            'propagate': True,
        },
        # Add any other logger-specific configuration here.
    }
}

# Google keys (google analytics...)
GOOGLE_ANALYTICS_KEY = env('GOOGLE_ANALYTICS_KEY')

# Tell django-storages the domain to use to refer to static files.
STATICFILES_LOCATION = 'static2'
STATICFILES_STORAGE = 'whitenoise.storage.CompressedStaticFilesStorage'
# STATICFILES_STORAGE = 'custom_storage.StaticStorage'
STATIC_ROOT = BASE_DIR / 'static2'
STATIC_HOST = env('STATIC_HOST')
STATIC_URL = STATIC_HOST + '/static2/'

STATICFILES_DIR = [
    BASE_DIR / 'static2',
]


MEDIA_URL = '/media/'
MEDIAFILES_LOCATION = 'media'
DEFAULT_FILE_STORAGE = 'custom_storage.MediaStorage'

CORS_ALLOW_METHODS = [
    'DELETE',
    'GET',
    'OPTIONS',
    'PATCH',
    'POST',
    'PUT',
]

CORS_ORIGIN_ALLOW_ALL = True

#CORS_ORIGIN_WHITELIST=[ 'https://localhost:3000', 'http://localhost:3000', 'https://niug.digitalvirtualspaces.com', 'https://trinity.digitalvirtualspaces.com', 'https://develop.digitalvirtualspaces.com', 'https://housing.digitalvirtualspaces.com', 'https://cnar.digitalvirtualspaces.com', 'https://demo.digitalvirtualspaces.com']

CORS_ALLOW_HEADERS = [
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
    'Access-Control-Allow-Origin',
    'x-force-update'
]

REST_FRAMEWORK = {
    'DEFAULT_PERMISSION_CLASSES': (
        'rest_framework.permissions.IsAuthenticated',
        "rest_framework_api_key.permissions.HasAPIKey",
    ),
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework_simplejwt.authentication.JWTAuthentication',
        'rest_framework.authentication.BasicAuthentication',
        'rest_framework.authentication.SessionAuthentication',
    ),
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 100,
}

# JWT_AUTH = {
#     'JWT_VERIFY': True,
#     'JWT_VERIFY_EXPIRATION': True,
#     'JWT_EXPIRATION_DELTA': datetime.timedelta(hours=10),
#     'JWT_AUTH_HEADER_PREFIX': 'Bearer',
# }

SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': datetime.timedelta(days=30),
}

LOGIN_REDIRECT_URL = '/'
AWS_S3_OBJECT_PARAMETERS = {
    'Expires': 'Thu, 31 Dec 2099 20:00:00 GMT',
    'CacheControl': 'max-age=94608000',
}

DATA_UPLOAD_MAX_NUMBER_FIELDS = None
DATA_UPLOAD_MAX_MEMORY_SIZE = None

TINYMCE_JS_URL = env('TINYMCE_JS_URL')
TINYMCE_COMPRESSOR = False
TINYMCE_DEFAULT_CONFIG = {
    'height': '320px',
    'width': '60rem',
    'menubar': 'file edit view insert format tools table help',
    'plugins': 'advlist autolink lists link image charmap print preview anchor searchreplace visualblocks code '
    'fullscreen insertdatetime media table paste code help wordcount spellchecker',
    'toolbar': 'undo redo | bold italic underline strikethrough | fontselect fontsizeselect formatselect | alignleft '
    'aligncenter alignright alignjustify | outdent indent |  numlist bullist checklist | forecolor '
    'backcolor casechange permanentpen formatpainter removeformat | pagebreak | charmap emoticons | '
    'fullscreen  preview save print | insertfile image media pageembed template link anchor codesample | '
    'a11ycheck ltr rtl | showcomments addcomment code',
    'custom_undo_redo_levels': 30,
    'valid_children': '+body[style]',
    'extended_valid_elements': 'script[language|type|src]',
    'force_br_newlines': False,
    'force_p_newlines': False,
    'forced_root_block': '',
}

DEFAULT_AUTO_FIELD = 'django.db.models.AutoField'

TYPEFORM_API_KEY = env('TYPEFORM_API_KEY')

MUX_TOKEN_ID = env('MUX_TOKEN_ID')
MUX_TOKEN_SECRET = env('MUX_TOKEN_SECRET')


# pool = ConnectionPool(
#     host='redis',
#     port=6379,
#     max_connections=4)
# HUEY = RedisHuey('auditorium', connection_pool=pool)

# HUEY = {
#     'huey_class': 'huey.RedisHuey',  # Huey implementation to use.
#     'name': DATABASES['default']['NAME'],  # Use db name for huey.
#     'results': True,  # Store return values of tasks.
#     'store_none': False,  # If a task returns None, do not save to results.
#     'immediate': False,  # If DEBUG=True, run synchronously.
#     'immediate_use_memory': False,
#     'utc': True,  # Use UTC for all times internally.
#     'blocking': True,  # Perform blocking pop rather than poll Redis.
#     'connection': {
#         'connection_pool': pool,  # Definitely you should use pooling!
#         # ... tons of other options, see redis-py for details.

#         # huey-specific connection parameters.
#         'read_timeout': 1,  # If not polling (blocking pop), use timeout.
#         'url': None,  # Allow Redis config via a DSN.
#     },
#     'consumer': {
#         'workers': 2,
#         'worker_type': 'process',
#         'initial_delay': 0.1,  # Smallest polling interval, same as -d.
#         'backoff': 1.15,  # Exponential backoff using this rate, -b.
#         'max_delay': 10.0,  # Max possible polling interval, -m.
#         'scheduler_interval': 1,  # Check schedule every second, -s.
#         'periodic': False,  # Enable crontab feature.
#         'check_worker_health': True,  # Enable worker health checks.
#         'health_check_interval': 1,  # Check worker health every second.
#     },
# }


# if DEBUG:
#     MIDDLEWARE += (
#        'debug_toolbar.middleware.DebugToolbarMiddleware',
#     #    'debug_toolbar.middleware.show_toolbar'
#     )

#     INSTALLED_APPS += (
#        'debug_toolbar',
#     )

#     INTERNAL_IPS = [
#         '127.0.0.1',
#         '0.0.0.0',
#         'localhost',
#         'dev.localhost',
#     ]

#     DEBUG_TOOLBAR_PANELS = [
#         'debug_toolbar.panels.history.HistoryPanel',
#         'debug_toolbar.panels.versions.VersionsPanel',
#         'debug_toolbar.panels.timer.TimerPanel',
#         'debug_toolbar.panels.settings.SettingsPanel',
#         'debug_toolbar.panels.headers.HeadersPanel',
#         'debug_toolbar.panels.request.RequestPanel',
#         'debug_toolbar.panels.sql.SQLPanel',
#         'debug_toolbar.panels.staticfiles.StaticFilesPanel',
#         'debug_toolbar.panels.templates.TemplatesPanel',
#         'debug_toolbar.panels.cache.CachePanel',
#         'debug_toolbar.panels.signals.SignalsPanel',
#         'debug_toolbar.panels.logging.LoggingPanel',
#         'debug_toolbar.panels.redirects.RedirectsPanel',
#         'debug_toolbar.panels.profiling.ProfilingPanel',
#     ]

#     def show_toolbar(request):
#         return True

#     DEBUG_TOOLBAR_CONFIG = {
#         'SHOW_TOOLBAR_CALLBACK': show_toolbar,
#         'SHOW_COLLAPSED': True,
#         'RENDER_PANELS': True,
#         'INTERCEPT_REDIRECTS': False,
#     }

    # mimetypes.add_type("application/javascript", ".js", True)

# HTTPS settings
# if 'ENVIRONMENT' in os.environ:
#         if os.environ['ENVIRONMENT'] != 'development':
#             SECURE_SSL_REDIRECT = True

#CSRF_COOKIE_SECURE = True
# SESSION_COOKIE_SECURE = True

# CSRF_COOKIE_DOMAIN = '.mbvs-server.com'
# CSRF_COOKIE_SAMESITE = None

# SESSION_COOKIE_SAMESITE = None
# SESSION_SAVE_EVERY_REQUEST = True

# # HSTS settings
# SECURE_HSTS_SECONDS = 31536000 # 1 year
# SECURE_HSTS_PRELOAD = True
# SECURE_HSTS_INCLUDE_SUBDOMAINS = True

# SECURE_SSL_HOST = 
